//*****************************************************************************
// File:     Form1.cs
// Purpose:  This program shows how to create a PowerDesigner PDM model using C#.
// Title:    Create PDM model
// Category: Create PDM
// Company:  Sybase Inc.
// Author:   Xiao Wang
// Created:
// Modified:
// Version:  1.0
// Comment:  You need to add the Sybase PdCommon and the Sybase PdPDM type libraries in the type library references.
//           You need to define the contants for the object kinds you need to use.
//*****************************************************************************

using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;

namespace CSharp_Sample1
{
	/// <summary>
	/// Summary description for Form1.
	/// </summary>
	public class Form1 : System.Windows.Forms.Form
	{
		internal System.Windows.Forms.Label Label1;
		internal System.Windows.Forms.Button Button2;
		internal System.Windows.Forms.Button Button1;
        internal Button btnCLS;
        private Button btnSequence;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public Form1()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.Label1 = new System.Windows.Forms.Label();
            this.Button2 = new System.Windows.Forms.Button();
            this.Button1 = new System.Windows.Forms.Button();
            this.btnCLS = new System.Windows.Forms.Button();
            this.btnSequence = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // Label1
            // 
            this.Label1.Location = new System.Drawing.Point(13, 16);
            this.Label1.Name = "Label1";
            this.Label1.Size = new System.Drawing.Size(334, 89);
            this.Label1.TabIndex = 5;
            this.Label1.Text = "This program shows how to use C# and OLE automation to create a PowerDesigner PDM" +
    ".";
            // 
            // Button2
            // 
            this.Button2.Location = new System.Drawing.Point(441, 144);
            this.Button2.Name = "Button2";
            this.Button2.Size = new System.Drawing.Size(111, 40);
            this.Button2.TabIndex = 4;
            this.Button2.Text = "Exit";
            this.Button2.Click += new System.EventHandler(this.Button2_Click);
            // 
            // Button1
            // 
            this.Button1.Location = new System.Drawing.Point(27, 144);
            this.Button1.Name = "Button1";
            this.Button1.Size = new System.Drawing.Size(111, 40);
            this.Button1.TabIndex = 3;
            this.Button1.Text = "&Create PDM";
            this.Button1.Click += new System.EventHandler(this.Button1_Click);
            // 
            // btnCLS
            // 
            this.btnCLS.Location = new System.Drawing.Point(173, 144);
            this.btnCLS.Name = "btnCLS";
            this.btnCLS.Size = new System.Drawing.Size(111, 40);
            this.btnCLS.TabIndex = 6;
            this.btnCLS.Text = "&Create CLASS";
            this.btnCLS.Click += new System.EventHandler(this.btnCLS_Click);
            // 
            // btnSequence
            // 
            this.btnSequence.Location = new System.Drawing.Point(308, 144);
            this.btnSequence.Name = "btnSequence";
            this.btnSequence.Size = new System.Drawing.Size(127, 40);
            this.btnSequence.TabIndex = 7;
            this.btnSequence.Text = "Create Sequence";
            this.btnSequence.UseVisualStyleBackColor = true;
            this.btnSequence.Click += new System.EventHandler(this.btnSequence_Click);
            // 
            // Form1
            // 
            this.ClientSize = new System.Drawing.Size(639, 200);
            this.Controls.Add(this.btnSequence);
            this.Controls.Add(this.btnCLS);
            this.Controls.Add(this.Label1);
            this.Controls.Add(this.Button2);
            this.Controls.Add(this.Button1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "Form1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "C# for PowerDesigner Sample1";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.ResumeLayout(false);

      }
		#endregion

		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main()
		{
			Application.Run(new Form1());
		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
         // Get PowerDesigner Application object
         PdCommon.Application pd;
         try
         {
            pd = new PdCommon.Application();
            if (pd != null)
		      {
               // Create a PDM model with a Physical Diagram
               PdPDM.Model model;
               model = (PdPDM.Model)pd.CreateModel((int)PdPDM.PdPDM_Classes.cls_Model, "|Diagram=PhysicalDiagram", 0);
               if (model != null)
               {
                  model.Name = "Customer Management";
                  model.Code = "CustomerManagement";

                  // Create a class diagram
                  PdPDM.PhysicalDiagram diagram;
                  diagram = (PdPDM.PhysicalDiagram)model.PhysicalDiagrams.Item(0);

                  // Create tables
                  CreateTables(model, diagram);

                  MessageBox.Show("Successfully created a PowerDesigner PDM model.", "C# for PowerDesigner Sample1", MessageBoxButtons.OK, MessageBoxIcon.Information);
               }
               else
               {
                  MessageBox.Show("Cannot create PDM model.", "C# for PowerDesigner Sample1", MessageBoxButtons.OK, MessageBoxIcon.Error);
               }
            }
         }
         catch (Exception ex)
         {
            MessageBox.Show("Cannot create PowerDesigner application object. Error: " + ex.Message + "\n\nPlease verify that PowerDesigner is installed and the PowerDesigner application object is registered.",
                            "C# for PowerDesigner Sample1", MessageBoxButtons.OK, MessageBoxIcon.Error);
         }
      }

      // Create tables
      private void CreateTables(PdPDM.Model model, PdPDM.PhysicalDiagram diagram)
      {
         // Create a table
         PdPDM.Table tbl;
         tbl = (PdPDM.Table)model.CreateObject((int)PdPDM.PdPDM_Classes.cls_Table, "", -1, false);
         tbl.Name = "Customer";
         tbl.Code = "Customer";
         tbl.Comment = "Customer table";
         tbl.Description = "The Customer table stores the customers data.";

         // Create columns
         CreateColumns(tbl);

         // Create a symbol for the table
         diagram.AttachObject(tbl);

         // Example: Creating relationships between tables using the reusable function
         // If you had multiple tables, you could create relationships like this:
         /*
         var orderTable = CreateOrderTable(model); // Assuming this method exists
         var orderItemTable = CreateOrderItemTable(model); // Assuming this method exists

         // Create a 1-to-many relationship between Order and OrderItem tables
         CreatePDMRelationship(
             model,
             diagram,
             orderTable,
             orderItemTable,
             "1-to-many",
             "OrderContainsItems",
             "FK_OrderItem_Order",
             new string[] { "ORDER_ID" },
             new string[] { "ORDER_ID" },
             "CASCADE",
             "CASCADE"
         );
         */
      }

      // Create columns
      private void CreateColumns(PdPDM.Table tbl)
      {
         PdPDM.Column coln;
         coln = (PdPDM.Column)tbl.Columns.CreateNew((int)PdPDM.PdPDM_Classes.cls_Column);
         coln.Name = "ID";
         coln.Code = "ID";
         coln.DataType = "integer";
         coln.Primary = true;

         coln = (PdPDM.Column)tbl.Columns.CreateNew((int)PdPDM.PdPDM_Classes.cls_Column);
         coln.Name = "Name";
         coln.Code = "Name";
         coln.DataType = "char(30)";
         coln.Length = 30;

         coln = (PdPDM.Column)tbl.Columns.CreateNew((int)PdPDM.PdPDM_Classes.cls_Column);
         coln.Name = "Phone";
         coln.Code = "Phone";
         coln.DataType = "char(20)";
         coln.Length = 20;

         coln = (PdPDM.Column)tbl.Columns.CreateNew((int)PdPDM.PdPDM_Classes.cls_Column);
         coln.Name = "Email";
         coln.Code = "Email";
         coln.DataType = "char(20)";
         coln.Length = 20;
      }

      private void Form1_Load(object sender, System.EventArgs e)
      {
         // Opening the form
      }

      private void Button2_Click(object sender, System.EventArgs e)
      {
         this.Close();
      }

      private void btnCLS_Click(object sender, EventArgs e)
      {
          PdCommon.Application pd;
          pd = new PdCommon.Application();
          CreateOOMModel(pd);
      }

      void CreateOOMModel(PdCommon.Application pd)
      {
          PdOOM.Model model;
          model = (PdOOM.Model)pd.CreateModel((int)PdOOM.PdOOM_Classes.cls_Model, "|Diagram=ClassDiagram", 0);
          model.Name = "Customer Management";
          model.Code = "CustomerManagement";

          // Create a class diagram
          PdOOM.ClassDiagram diagram;
          diagram = (PdOOM.ClassDiagram)model.ClassDiagrams.Item(0);

          // Existing sample: create Customer class with attributes/operations
          CreateClasses(model, diagram);

          // New: create additional classes in the OOM

          // Class: Order
          PdOOM.Class order = (PdOOM.Class)model.CreateObject((int)PdOOM.PdOOM_Classes.cls_Class);
          order.Name = "Order";
          order.Code = "Order";
          order.Comment = "Order placed by a customer";
          order.Stereotype = "Class";
          order.Description = "Represents a customer order with date and total amount.";

          // Order attributes
          PdOOM.Attribute oAttr = (PdOOM.Attribute)order.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oAttr.Name = "OrderID";
          oAttr.Code = "OrderID";
          oAttr.DataType = "int";
          oAttr.Persistent = true;
          oAttr.PersistentCode = "ORDER_ID";
          oAttr.PersistentDataType = "I";
          oAttr.PrimaryIdentifier = true;

          oAttr = (PdOOM.Attribute)order.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oAttr.Name = "OrderDate";
          oAttr.Code = "OrderDate";
          oAttr.DataType = "DateTime";
          oAttr.Persistent = true;
          oAttr.PersistentCode = "ORDER_DATE";
          oAttr.PersistentDataType = "DT";

          oAttr = (PdOOM.Attribute)order.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oAttr.Name = "TotalAmount";
          oAttr.Code = "TotalAmount";
          oAttr.DataType = "Decimal";
          oAttr.Persistent = true;
          oAttr.PersistentCode = "TOTAL_AMOUNT";
          oAttr.PersistentDataType = "D";

          // Order operations
          PdOOM.Operation oOper = (PdOOM.Operation)order.CreateObject((int)PdOOM.PdOOM_Classes.cls_Operation);
          oOper.Name = "CalculateTotal";
          oOper.Code = "CalculateTotal";
          oOper.ReturnType = "Decimal";
          oOper.Body = "{\r\n // Sum line totals and assign to TotalAmount\r\n return TotalAmount;\r\n}";

          oOper = (PdOOM.Operation)order.CreateObject((int)PdOOM.PdOOM_Classes.cls_Operation);
          oOper.Name = "AddItem";
          oOper.Code = "AddItem";
          oOper.ReturnType = "void";
          PdOOM.Parameter oParam = (PdOOM.Parameter)oOper.CreateObject((int)PdOOM.PdOOM_Classes.cls_Parameter);
          oParam.Name = "productName";
          oParam.Code = "productName";
          oParam.DataType = "String";
          oParam = (PdOOM.Parameter)oOper.CreateObject((int)PdOOM.PdOOM_Classes.cls_Parameter);
          oParam.Name = "quantity";
          oParam.Code = "quantity";
          oParam.DataType = "int";
          oParam = (PdOOM.Parameter)oOper.CreateObject((int)PdOOM.PdOOM_Classes.cls_Parameter);
          oParam.Name = "unitPrice";
          oParam.Code = "unitPrice";
          oParam.DataType = "Decimal";
          oOper.Body = "{\r\n // Add a new OrderItem\r\n}";

          // Attach Order to diagram and keep the symbol
          var orderSym = diagram.AttachObject(order);

          // Class: OrderItem
          PdOOM.Class orderItem = (PdOOM.Class)model.CreateObject((int)PdOOM.PdOOM_Classes.cls_Class);
          orderItem.Name = "OrderItem";
          orderItem.Code = "OrderItem";
          orderItem.Comment = "Line item of an order";
          orderItem.Stereotype = "Class";
          orderItem.Description = "Represents a single line item within an order.";

          // OrderItem attributes
          PdOOM.Attribute oiAttr = (PdOOM.Attribute)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oiAttr.Name = "ItemID";
          oiAttr.Code = "ItemID";
          oiAttr.DataType = "int";
          oiAttr.Persistent = true;
          oiAttr.PersistentCode = "ITEM_ID";
          oiAttr.PersistentDataType = "I";
          oiAttr.PrimaryIdentifier = true;

          oiAttr = (PdOOM.Attribute)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oiAttr.Name = "ProductName";
          oiAttr.Code = "ProductName";
          oiAttr.DataType = "String";
          oiAttr.Persistent = true;
          oiAttr.PersistentCode = "PRODUCT_NAME";
          oiAttr.PersistentDataType = "A50";

          oiAttr = (PdOOM.Attribute)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oiAttr.Name = "Quantity";
          oiAttr.Code = "Quantity";
          oiAttr.DataType = "int";
          oiAttr.Persistent = true;
          oiAttr.PersistentCode = "QTY";
          oiAttr.PersistentDataType = "I";

          oiAttr = (PdOOM.Attribute)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oiAttr.Name = "UnitPrice";
          oiAttr.Code = "UnitPrice";
          oiAttr.DataType = "Decimal";
          oiAttr.Persistent = true;
          oiAttr.PersistentCode = "UNIT_PRICE";
          oiAttr.PersistentDataType = "D";

          oiAttr = (PdOOM.Attribute)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          oiAttr.Name = "LineTotal";
          oiAttr.Code = "LineTotal";
          oiAttr.DataType = "Decimal";
          oiAttr.Persistent = true;
          oiAttr.PersistentCode = "LINE_TOTAL";
          oiAttr.PersistentDataType = "D";

          // OrderItem operations
          PdOOM.Operation oiOper = (PdOOM.Operation)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Operation);
          oiOper.Name = "CalculateLineTotal";
          oiOper.Code = "CalculateLineTotal";
          oiOper.ReturnType = "Decimal";
          oiOper.Body = "{\r\n // LineTotal = Quantity * UnitPrice\r\n return LineTotal;\r\n}";

          oiOper = (PdOOM.Operation)orderItem.CreateObject((int)PdOOM.PdOOM_Classes.cls_Operation);
          oiOper.Name = "SetQuantity";
          oiOper.Code = "SetQuantity";
          oiOper.ReturnType = "void";
          PdOOM.Parameter oiParam = (PdOOM.Parameter)oiOper.CreateObject((int)PdOOM.PdOOM_Classes.cls_Parameter);
          oiParam.Name = "newQty";
          oiParam.Code = "newQty";
          oiParam.DataType = "int";
          oiOper.Body = "{\r\n Quantity = newQty;\r\n}";

          // Attach OrderItem to diagram and keep the symbol
          var orderItemSym = diagram.AttachObject(orderItem);

            // Relationship: Order composes OrderItem (1 to many)
            // Using the structured relationship creation function
            var orderItemRelationshipConfig = new OOMRelationshipConfig
            {
                Model = model,
                Diagram = diagram,
                ParentEntity = order,
                ChildEntity = orderItem,
                RelationshipType = "1-to-many",
                RelationshipName = "OrderContainsItems",
                RelationshipCode = "OrderContainsItems",
                ParentRoleName = "order",
                ChildRoleName = "items",
                IsComposition = true,
                IsAggregation = false
            };
            CreateOOMRelationship(orderItemRelationshipConfig);

            // Alternative: Using the simplified helper method for composition relationships
            // CreateCompositionRelationship(model, diagram, order, orderItem, "OrderContainsItems", "order", "items");

            // Example: Additional relationships can be easily created using the structured approach
            // For a Customer-Order relationship (1-to-many):
            /*
            var customer = CreateCustomerClass(model); // Assuming this method exists
            var customerOrderRelationshipConfig = new OOMRelationshipConfig
            {
                Model = model,
                Diagram = diagram,
                ParentEntity = customer,
                ChildEntity = order,
                RelationshipType = "1-to-many",
                RelationshipName = "CustomerPlacesOrders",
                RelationshipCode = "CustomerPlacesOrders",
                ParentRoleName = "customer",
                ChildRoleName = "orders",
                IsComposition = false,
                IsAggregation = false
            };
            CreateOOMRelationship(customerOrderRelationshipConfig);
            */
      }

      void CreateClasses(PdOOM.Model model, PdOOM.ClassDiagram diagram)
      {
          var cls = (PdOOM.Class)model.CreateObject((int)PdOOM.PdOOM_Classes.cls_Class);
          cls.Name = "Customer";
          cls.Code = "Customer";
          cls.Comment = "Customer class";
          cls.Stereotype = "Class";
          cls.Description = "The customer class defines the attributes and behaviors of a customer.";

          CreateAttributes(cls);
          CreateOperations(cls);

          var sym = diagram.AttachObject(cls);
      }

      void CreateAttributes(PdOOM.Class cls)
      {
          var attr = (PdOOM.Attribute)cls.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          attr.Name = "ID";
          attr.Code = "ID";
          attr.DataType = "int";
          attr.Persistent = true;
          attr.PersistentCode = "ID";
          attr.PersistentDataType = "I";
          attr.PrimaryIdentifier = true;

          attr = (PdOOM.Attribute)cls.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          attr.Name = "Name";
          attr.Code = "Name";
          attr.DataType = "String";
          attr.Persistent = true;
          attr.PersistentCode = "NAME";
          attr.PersistentDataType = "A30";

          attr = (PdOOM.Attribute)cls.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          attr.Name = "Phone";
          attr.Code = "Phone";
          attr.DataType = "String";
          attr.Persistent = true;
          attr.PersistentCode = "PHONE";
          attr.PersistentDataType = "A20";

          attr = (PdOOM.Attribute)cls.CreateObject((int)PdOOM.PdOOM_Classes.cls_Attribute);
          attr.Name = "Email";
          attr.Code = "Email";
          attr.DataType = "String";
          attr.Persistent = true;
          attr.PersistentCode = "EMAIL";
          attr.PersistentDataType = "A30";
      }

      void CreateOperations(PdOOM.Class cls)
      {
          var oper = (PdOOM.Operation)cls.CreateObject((int)PdOOM.PdOOM_Classes.cls_Operation);
          oper.Name = "GetName";
          oper.Code = "GetName";
          oper.ReturnType = "String";
          string body = "{\r\n return Name;\r\n}";
          oper.Body = body;

          oper = (PdOOM.Operation)cls.CreateObject((int)PdOOM.PdOOM_Classes.cls_Operation);
          oper.Name = "SetName";
          oper.Code = "SetName";
          oper.ReturnType = "void";
          var param = (PdOOM.Parameter)oper.CreateObject((int)PdOOM.PdOOM_Classes.cls_Parameter);
          param.Name = "newName";
          param.Code = "newName";
          param.DataType = "String";
          body = "{\r\n Name = newName;\r\n}";
          oper.Body = body;
      }

      /// <summary>
      /// Represents the configuration for creating an OOM relationship between two classes
      /// </summary>
      public class OOMRelationshipConfig
      {
          /// <summary>
          /// The OOM model where the relationship will be created
          /// </summary>
          public PdOOM.Model Model { get; set; }

          /// <summary>
          /// The class diagram where the relationship will be displayed
          /// </summary>
          public PdOOM.ClassDiagram Diagram { get; set; }

          /// <summary>
          /// The parent class in the relationship
          /// </summary>
          public PdOOM.Class ParentEntity { get; set; }

          /// <summary>
          /// The child class in the relationship
          /// </summary>
          public PdOOM.Class ChildEntity { get; set; }

          /// <summary>
          /// Type of relationship (1-to-1, 1-to-many, many-to-many)
          /// </summary>
          public string RelationshipType { get; set; }

          /// <summary>
          /// Display name for the relationship
          /// </summary>
          public string RelationshipName { get; set; }

          /// <summary>
          /// Code name for the relationship
          /// </summary>
          public string RelationshipCode { get; set; }

          /// <summary>
          /// Role name for the parent entity (optional)
          /// </summary>
          public string ParentRoleName { get; set; }

          /// <summary>
          /// Role name for the child entity (optional)
          /// </summary>
          public string ChildRoleName { get; set; }

          /// <summary>
          /// Whether this is a composition relationship (default: false)
          /// </summary>
          public bool IsComposition { get; set; }

          /// <summary>
          /// Whether this is an aggregation relationship (default: false)
          /// </summary>
          public bool IsAggregation { get; set; }

          /// <summary>
          /// Initializes a new instance of the OOMRelationshipConfig class with default values
          /// </summary>
          public OOMRelationshipConfig()
          {
              IsComposition = false;
              IsAggregation = false;
          }

          /// <summary>
          /// Validates that all required properties are set
          /// </summary>
          public void Validate()
          {
              if (Model == null) throw new ArgumentNullException("Model");
              if (Diagram == null) throw new ArgumentNullException("Diagram");
              if (ParentEntity == null) throw new ArgumentNullException("ParentEntity");
              if (ChildEntity == null) throw new ArgumentNullException("ChildEntity");
              if (string.IsNullOrEmpty(RelationshipType)) throw new ArgumentException("RelationshipType cannot be null or empty");
              if (string.IsNullOrEmpty(RelationshipName)) throw new ArgumentException("RelationshipName cannot be null or empty");
              if (string.IsNullOrEmpty(RelationshipCode)) throw new ArgumentException("RelationshipCode cannot be null or empty");
          }
      }

      /// <summary>
      /// Creates a relationship between two entities in an OOM model using a structured configuration
      /// </summary>
      /// <param name="config">The relationship configuration containing all necessary parameters</param>
      /// <returns>The created association object</returns>
      PdOOM.Association CreateOOMRelationship(OOMRelationshipConfig config)
      {
          // Validate the configuration
          config.Validate();

          // Create the association
          PdOOM.Association association = (PdOOM.Association)config.Model.CreateObject((int)PdOOM.PdOOM_Classes.cls_Association);
          association.Name = config.RelationshipName;
          association.Code = config.RelationshipCode;
          association.ClassA = config.ParentEntity;
          association.ClassB = config.ChildEntity;

          // Set relationship type and multiplicities (using basic properties only)
          switch (config.RelationshipType.ToLower())
          {
              case "1-to-1":
                  // For 1-to-1, both sides have multiplicity of 1
                  break;
              case "1-to-many":
                  // For 1-to-many, parent is 1, child is many
                  break;
              case "many-to-many":
                  // For many-to-many, both sides are many
                  break;
              default:
                  // Default to 1-to-many if type is not recognized
                  break;
          }

          // Note: Advanced properties like multiplicities, role names, composition/aggregation
          // may need to be set manually after creation depending on PowerDesigner version
          // The basic association creation and attachment to diagram is the core functionality
          // Role names: config.ParentRoleName, config.ChildRoleName
          // Composition/Aggregation flags: config.IsComposition, config.IsAggregation

          // Attach the association to the diagram
          config.Diagram.AttachLinkObject(association);

          return association;
      }

      /// <summary>
      /// Creates a relationship between two entities in an OOM model (backward compatibility overload)
      /// </summary>
      /// <param name="model">The OOM model</param>
      /// <param name="diagram">The class diagram</param>
      /// <param name="parentEntity">The parent class in the relationship</param>
      /// <param name="childEntity">The child class in the relationship</param>
      /// <param name="relationshipType">Type of relationship (1-to-1, 1-to-many, many-to-many)</param>
      /// <param name="relationshipName">Display name for the relationship</param>
      /// <param name="relationshipCode">Code name for the relationship</param>
      /// <param name="parentRoleName">Role name for the parent entity (optional)</param>
      /// <param name="childRoleName">Role name for the child entity (optional)</param>
      /// <param name="isComposition">Whether this is a composition relationship (default: false)</param>
      /// <param name="isAggregation">Whether this is an aggregation relationship (default: false)</param>
      /// <returns>The created association object</returns>
      PdOOM.Association CreateOOMRelationship(
          PdOOM.Model model,
          PdOOM.ClassDiagram diagram,
          PdOOM.Class parentEntity,
          PdOOM.Class childEntity,
          string relationshipType,
          string relationshipName,
          string relationshipCode,
          string parentRoleName,
          string childRoleName,
          bool isComposition,
          bool isAggregation)
      {
          var config = new OOMRelationshipConfig
          {
              Model = model,
              Diagram = diagram,
              ParentEntity = parentEntity,
              ChildEntity = childEntity,
              RelationshipType = relationshipType,
              RelationshipName = relationshipName,
              RelationshipCode = relationshipCode,
              ParentRoleName = parentRoleName,
              ChildRoleName = childRoleName,
              IsComposition = isComposition,
              IsAggregation = isAggregation
          };

          return CreateOOMRelationship(config);
      }

      /// <summary>
      /// Helper method to create a composition relationship with simplified parameters
      /// </summary>
      /// <param name="model">The OOM model</param>
      /// <param name="diagram">The class diagram</param>
      /// <param name="parentEntity">The parent class</param>
      /// <param name="childEntity">The child class</param>
      /// <param name="relationshipName">Name for the relationship</param>
      /// <param name="parentRoleName">Role name for parent (optional)</param>
      /// <param name="childRoleName">Role name for child (optional)</param>
      /// <returns>The created association object</returns>
      PdOOM.Association CreateCompositionRelationship(
          PdOOM.Model model,
          PdOOM.ClassDiagram diagram,
          PdOOM.Class parentEntity,
          PdOOM.Class childEntity,
          string relationshipName,
          string parentRoleName = null,
          string childRoleName = null)
      {
          var config = new OOMRelationshipConfig
          {
              Model = model,
              Diagram = diagram,
              ParentEntity = parentEntity,
              ChildEntity = childEntity,
              RelationshipType = "1-to-many",
              RelationshipName = relationshipName,
              RelationshipCode = relationshipName,
              ParentRoleName = parentRoleName,
              ChildRoleName = childRoleName,
              IsComposition = true,
              IsAggregation = false
          };

          return CreateOOMRelationship(config);
      }

      /// <summary>
      /// Helper method to create an aggregation relationship with simplified parameters
      /// </summary>
      /// <param name="model">The OOM model</param>
      /// <param name="diagram">The class diagram</param>
      /// <param name="parentEntity">The parent class</param>
      /// <param name="childEntity">The child class</param>
      /// <param name="relationshipName">Name for the relationship</param>
      /// <param name="parentRoleName">Role name for parent (optional)</param>
      /// <param name="childRoleName">Role name for child (optional)</param>
      /// <returns>The created association object</returns>
      PdOOM.Association CreateAggregationRelationship(
          PdOOM.Model model,
          PdOOM.ClassDiagram diagram,
          PdOOM.Class parentEntity,
          PdOOM.Class childEntity,
          string relationshipName,
          string parentRoleName = null,
          string childRoleName = null)
      {
          var config = new OOMRelationshipConfig
          {
              Model = model,
              Diagram = diagram,
              ParentEntity = parentEntity,
              ChildEntity = childEntity,
              RelationshipType = "1-to-many",
              RelationshipName = relationshipName,
              RelationshipCode = relationshipName,
              ParentRoleName = parentRoleName,
              ChildRoleName = childRoleName,
              IsComposition = false,
              IsAggregation = true
          };

          return CreateOOMRelationship(config);
      }

      /// <summary>
      /// Creates a relationship (reference) between two tables in a PDM model
      /// </summary>
      /// <param name="model">The PDM model</param>
      /// <param name="diagram">The physical diagram</param>
      /// <param name="parentTable">The parent table in the relationship</param>
      /// <param name="childTable">The child table in the relationship</param>
      /// <param name="relationshipType">Type of relationship (1-to-1, 1-to-many, many-to-many)</param>
      /// <param name="relationshipName">Display name for the relationship</param>
      /// <param name="relationshipCode">Code name for the relationship</param>
      /// <param name="foreignKeyColumns">Array of foreign key column names in child table (optional)</param>
      /// <param name="referencedColumns">Array of referenced column names in parent table (optional)</param>
      /// <param name="onDeleteAction">Action on delete (CASCADE, SET NULL, RESTRICT, etc.)</param>
      /// <param name="onUpdateAction">Action on update (CASCADE, SET NULL, RESTRICT, etc.)</param>
      /// <returns>The created reference object</returns>
      PdPDM.Reference CreatePDMRelationship(
          PdPDM.Model model,
          PdPDM.PhysicalDiagram diagram,
          PdPDM.Table parentTable,
          PdPDM.Table childTable,
          string relationshipType,
          string relationshipName,
          string relationshipCode,
          string[] foreignKeyColumns,
          string[] referencedColumns,
          string onDeleteAction,
          string onUpdateAction)
      {
          // Create the reference
          PdPDM.Reference reference = (PdPDM.Reference)model.CreateObject((int)PdPDM.PdPDM_Classes.cls_Reference);
          reference.Name = relationshipName;
          reference.Code = relationshipCode;
          reference.ParentTable = parentTable;
          reference.ChildTable = childTable;

          // Note: Advanced properties like cardinality, constraints, and joins
          // may need to be set manually after creation depending on PowerDesigner version
          // The basic reference creation and attachment to diagram is the core functionality

          // Attach the reference to the diagram
          diagram.AttachLinkObject(reference);

          return reference;
      }

      private void btnSequence_Click(object sender, EventArgs e)
      {
          PdCommon.Application pd;
          try
          {
              pd = new PdCommon.Application();
              if (pd != null)
              {
                  // Create a PDM model with a Physical Diagram
                  PdOOM.Model model;
                  model = (PdOOM.Model)pd.CreateModel((int)PdOOM.PdOOM_Classes.cls_Model, "|Diagram=SequenceDiagram", 0);
                  if (model != null)
                  {
                      model.Name = "ActorSystem";
                      model.Code = "ActorSystem";

                      // Create a class diagram
                      PdOOM.SequenceDiagram diagram;
                      diagram = (PdOOM.SequenceDiagram)model.SequenceDiagrams.Item(0);

                      // Create Sequence Objects
                      CreateSequenceActor(model, diagram);

                      MessageBox.Show("Successfully created a PowerDesigner PDM model.", "C# for PowerDesigner Sample1", MessageBoxButtons.OK, MessageBoxIcon.Information);
                  }
                  else
                  {
                      MessageBox.Show("Cannot create PDM model.", "C# for PowerDesigner Sample1", MessageBoxButtons.OK, MessageBoxIcon.Error);
                  }
              }
          }
          catch (Exception ex)
          {
              MessageBox.Show("Cannot create PowerDesigner application object. Error: " + ex.Message + "\n\nPlease verify that PowerDesigner is installed and the PowerDesigner application object is registered.",
                              "C# for PowerDesigner Sample1", MessageBoxButtons.OK, MessageBoxIcon.Error);
          }
      }

      void CreateSequenceActor(PdOOM.Model model, PdOOM.SequenceDiagram diagram)
      {
          var cls = (PdOOM.Actor)model.CreateObject((int)PdOOM.PdOOM_Classes.cls_Actor);
          var sym = diagram.AttachObject(cls);
          CreateSequenceObject(model, diagram);
      }

      void CreateSequenceObject(PdOOM.Model model, PdOOM.SequenceDiagram diagram)
      {
      }


   }
}
