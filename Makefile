# ----- Configuration -----
CONFIG = Debug
SLN_PLATFORM = Any CPU
SLN = CSharp Sample1.sln
PROJ = CSharp Sample1.csproj
ASSEMBLY_NAME = CSharp Sample1
OUT_EXE = bin\$(CONFIG)\$(ASSEMBLY_NAME).exe

# Correct path to Visual Studio 2012 devenv.com
DEVENV = D:\Program Files (x86)\Microsoft Visual Studio 11.0\Common7\IDE\devenv.com

# Default target
all: build

# Build/Clean/Rebuild via devenv
build:
	@cmd /C "echo Building $(SLN) [$(CONFIG)^^^|$(SLN_PLATFORM)]..."
	@cmd /C ""$(DEVENV)" "$(SLN)" /Build "$(CONFIG)^^|$(SLN_PLATFORM)" /Out build.log"
	@type build.log

rebuild:
	@cmd /C "echo Rebuilding $(SLN) [$(CONFIG)^^^|$(SLN_PLATFORM)]..."
	@cmd /C ""$(DEVENV)" "$(SLN)" /Rebuild "$(CONFIG)^^|$(SLN_PLATFORM)" /Out rebuild.log"
	@type rebuild.log

clean:
	@cmd /C "echo Cleaning $(SLN) [$(CONFIG)^^^|$(SLN_PLATFORM)]..."
	@cmd /C ""$(DEVENV)" "$(SLN)" /Clean "$(CONFIG)^^|$(SLN_PLATFORM)" /Out clean.log"
	@type clean.log
	-if exist bin rmdir /S /Q bin
	-if exist obj rmdir /S /Q obj

# Run (builds first)
run: build
	@echo Running $(OUT_EXE)
	@cmd /C ""$(OUT_EXE)""

# Release convenience targets
build-release:
	@cmd /C "echo Building $(SLN) [Release^^^|$(SLN_PLATFORM)]..."
	@cmd /C ""$(DEVENV)" "$(SLN)" /Build "Release^^|$(SLN_PLATFORM)" /Out build-release.log"
	@type build-release.log

rebuild-release:
	@cmd /C "echo Rebuilding $(SLN) [Release^^^|$(SLN_PLATFORM)]..."
	@cmd /C ""$(DEVENV)" "$(SLN)" /Rebuild "Release^^|$(SLN_PLATFORM)" /Out rebuild-release.log"
	@type rebuild-release.log

run-release: build-release
	@echo Running bin\Release\$(ASSEMBLY_NAME).exe
	@cmd /C ""bin\Release\$(ASSEMBLY_NAME).exe""

# Help
help:
	@echo Targets: build, rebuild, clean, run, build-release, rebuild-release, run-release
	@echo Edit DEVENV path in Makefile if Visual Studio is installed elsewhere.
